using Microsoft.AspNetCore.Mvc;
using MinimalApi.Models;
using MinimalApi.Services;

namespace MinimalApi.Endpoints;

public static class ProductEndpoints
{
    public static void MapProductEndpoints(this IEndpointRouteBuilder endpoints)
    {
        var group = endpoints.MapGroup("/api/products")
            .WithTags("Products")
            .WithOpenApi();

        // GET /api/products
        group.MapGet("/", async (IProductService productService) =>
        {
            var products = await productService.GetAllProductsAsync();
            return Results.Ok(products);
        })
        .WithName("GetAllProducts")
        .WithSummary("Get all products")
        .Produces<IEnumerable<ProductResponseDto>>(StatusCodes.Status200OK);

        // GET /api/products/{id}
        group.MapGet("/{id:int}", async (int id, IProductService productService) =>
        {
            var product = await productService.GetProductByIdAsync(id);
            return product == null ? Results.NotFound($"Product with ID {id} not found") : Results.Ok(product);
        })
        .WithName("GetProductById")
        .WithSummary("Get a product by ID")
        .Produces<ProductResponseDto>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status404NotFound);

        // POST /api/products
        group.MapPost("/", async ([FromBody] CreateProductDto createProductDto, IProductService productService) =>
        {
            var product = await productService.CreateProductAsync(createProductDto);
            return Results.Created($"/api/products/{product.Id}", product);
        })
        .WithName("CreateProduct")
        .WithSummary("Create a new product")
        .Accepts<CreateProductDto>("application/json")
        .Produces<ProductResponseDto>(StatusCodes.Status201Created)
        .Produces(StatusCodes.Status400BadRequest);

        // PUT /api/products/{id}
        group.MapPut("/{id:int}", async (int id, [FromBody] UpdateProductDto updateProductDto, IProductService productService) =>
        {
            var product = await productService.UpdateProductAsync(id, updateProductDto);
            return product == null ? Results.NotFound($"Product with ID {id} not found") : Results.Ok(product);
        })
        .WithName("UpdateProduct")
        .WithSummary("Update an existing product")
        .Accepts<UpdateProductDto>("application/json")
        .Produces<ProductResponseDto>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status404NotFound)
        .Produces(StatusCodes.Status400BadRequest);

        // DELETE /api/products/{id}
        group.MapDelete("/{id:int}", async (int id, IProductService productService) =>
        {
            var deleted = await productService.DeleteProductAsync(id);
            return deleted ? Results.NoContent() : Results.NotFound($"Product with ID {id} not found");
        })
        .WithName("DeleteProduct")
        .WithSummary("Delete a product")
        .Produces(StatusCodes.Status204NoContent)
        .Produces(StatusCodes.Status404NotFound);
    }
}
