# Minimal API ASP.NET Project

A well-structured ASP.NET Core Minimal API project with CRUD operations for product management.

## Project Structure

```
MinimalApi/
├── Data/
│   └── ApplicationDbContext.cs    # Entity Framework DbContext
├── Endpoints/
│   └── ProductEndpoints.cs        # API endpoint definitions
├── Models/
│   ├── Product.cs                 # Product entity model
│   └── ProductDto.cs              # Data Transfer Objects
├── Services/
│   ├── IProductService.cs         # Service interface
│   └── ProductService.cs          # Service implementation
├── Program.cs                     # Application entry point
└── README.md                      # This file
```

## Features

- **Clean Architecture**: Organized with separate layers for data, services, and endpoints
- **CRUD Operations**: Complete Create, Read, Update, Delete functionality for products
- **Entity Framework Core**: Using In-Memory database for development
- **Swagger/OpenAPI**: Interactive API documentation
- **Dependency Injection**: Proper service registration and injection
- **Data Validation**: Input validation using Data Annotations
- **CORS Support**: Cross-Origin Resource Sharing enabled
- **Health Check**: Basic health monitoring endpoint

## API Endpoints

### Products
- `GET /api/products` - Get all products
- `GET /api/products/{id}` - Get product by ID
- `POST /api/products` - Create new product
- `PUT /api/products/{id}` - Update existing product
- `DELETE /api/products/{id}` - Delete product

### Health
- `GET /health` - Health check endpoint

## Getting Started

### Prerequisites
- .NET 9.0 SDK or later

### Running the Application

1. Clone or download the project
2. Navigate to the project directory
3. Run the application:
   ```bash
   dotnet run
   ```
4. The API will be available at `http://localhost:5016`
5. Access Swagger UI at `http://localhost:5016/swagger`

### Sample Data

The application comes with pre-seeded sample data:
- Laptop ($1299.99, Stock: 10)
- Mouse ($29.99, Stock: 50)
- Keyboard ($149.99, Stock: 25)

## API Usage Examples

### Get All Products
```bash
GET http://localhost:5016/api/products
```

### Get Product by ID
```bash
GET http://localhost:5016/api/products/1
```

### Create New Product
```bash
POST http://localhost:5016/api/products
Content-Type: application/json

{
  "name": "Monitor",
  "description": "4K Gaming Monitor",
  "price": 399.99,
  "stock": 15
}
```

### Update Product
```bash
PUT http://localhost:5016/api/products/1
Content-Type: application/json

{
  "name": "Updated Laptop",
  "price": 1199.99
}
```

### Delete Product
```bash
DELETE http://localhost:5016/api/products/1
```

## Technologies Used

- **ASP.NET Core 9.0** - Web framework
- **Entity Framework Core** - ORM with In-Memory database
- **Swashbuckle.AspNetCore** - Swagger/OpenAPI documentation
- **System.ComponentModel.DataAnnotations** - Data validation

## Development Notes

- The project uses an in-memory database, so data will be lost when the application stops
- CORS is configured to allow all origins for development purposes
- All endpoints return JSON responses
- Proper HTTP status codes are used (200, 201, 404, etc.)
- Input validation is implemented using Data Annotations
