### Get all products
GET http://localhost:5016/api/products

### Get product by ID
GET http://localhost:5016/api/products/1

### Create new product
POST http://localhost:5016/api/products
Content-Type: application/json

{
  "name": "Gaming Headset",
  "description": "Wireless gaming headset with noise cancellation",
  "price": 199.99,
  "stock": 30
}

### Update product
PUT http://localhost:5016/api/products/1
Content-Type: application/json

{
  "name": "Updated Laptop Pro",
  "description": "High-performance laptop for development and gaming",
  "price": 1399.99,
  "stock": 8
}

### Partial update product
PUT http://localhost:5016/api/products/2
Content-Type: application/json

{
  "price": 34.99
}

### Delete product
DELETE http://localhost:5016/api/products/3

### Health check
GET http://localhost:5016/health

### Test non-existent product
GET http://localhost:5016/api/products/999
